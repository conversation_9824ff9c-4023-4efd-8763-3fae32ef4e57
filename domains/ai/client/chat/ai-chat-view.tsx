// import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import React from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import type { AIIntentParams, AIChatInputConfig, AIMessageBO } from '@bika/types/ai/bo';
import { type IAIChatInputStateContext } from '@bika/types/ai/context';
import type { AIChatContextVO, AIWizardVO } from '@bika/types/ai/vo';
import type { ISpaceContext } from '@bika/types/space/context';
import type { Locale } from '@bika/types/system';
import { useGlobalState } from '@bika/types/website/context';
import { Button } from '@bika/ui/button-component';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import { Skeleton } from '@bika/ui/skeleton';
import { snackbarShow } from '@bika/ui/snackbar';
import { type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AIChatUIRenderer } from './ai-chat-ui';
import { useSendAutoChatState } from '../wizard/use-send-auto-chat-state';

export interface AIChatViewProps {
  inputState: IAIChatInputStateContext;
  config?: AIChatInputConfig;

  // initUserMessage?: string;
  // use previous dialog, or new dialog?
  // initChatId?: string;
  // if new dialog, what's the intent type?
  initAIIntent?: AIIntentParams;

  onClose?: () => void;

  // 是否内置modal component
  withModal?: boolean;
  // 是否强制语言？会在 useChat的 custom body 传入
  forceLocale?: Locale;
  // 是否侧边栏模式
  displayMode?: 'COPILOT' | 'MODAL' | 'VIEW';

  // context?: AIChatContextVO[];
  // allowContextMenu?: AIChatContextVO['type'][];
  startNewChat?: () => void;
  skillsetIcons?: React.ReactNode;
}

export type IAIChatViewHandle = Omit<AIChatHandle, 'doAppendMessage'> & {
  doAppendMessage?: (msg: string, contexts?: AIChatContextVO[]) => void;
  reset: () => void;
};

/**
 * 使用Streaming AI Chat View 的Wizard2
 *
 * @returns
 */
function InternalAIChatView(props: AIChatViewProps, ref: React.Ref<IAIChatViewHandle>) {
  const { startNewChat, inputState } = props;
  const { trpc, trpcQuery } = useApiCaller();
  const { lang, t } = useLocale();
  const chatUIHandle = React.useRef<AIChatHandle>(null);
  const [globalSpaceContext] = useGlobalState<ISpaceContext>('SPACE');
  const [wizard, setWizard] = React.useState<AIWizardVO | null>(null);
  const spaceId = globalSpaceContext?.data.id;

  const { setData } = useSendAutoChatState();

  const [initError, setInitError] = React.useState<string | null>(null);

  const isWizardFetching = React.useRef(false);
  const newWizardCreatedRef = React.useRef(false);

  React.useImperativeHandle(ref, () => ({
    doAppendMessage: chatUIHandle.current?.doAppendMessage,
    reset: () => {
      isWizardFetching.current = false;
      newWizardCreatedRef.current = false;
    },
  }));

  // init start message 是否已经被执行完毕
  // const initStartMessageExecuted = React.useRef(false); // 这里有 ref，是避免 useEffect 开发环境下的副作用执行两次

  const createNewWizard = trpcQuery.ai.newWizard.useMutation();

  const initDialog = React.useCallback(
    async (wizardId?: string) => {
      isWizardFetching.current = true;
      if (wizardId) {
        const data = await trpc.ai.fetchWizard.query({ wizardId });
        inputState.setChatId(data.id);
        isWizardFetching.current = false;
        const w = data as AIWizardVO;
        setWizard(w);
        setInitError(null);
      } else {
        if (newWizardCreatedRef.current) {
          isWizardFetching.current = false;
          return;
        }
        newWizardCreatedRef.current = true;
        const data = await createNewWizard.mutateAsync({
          spaceId: spaceId!,
          intent: props.initAIIntent || {
            type: 'SEARCH', // default ai intent
          },
        });
        snackbarShow({
          content: t.wizard.new_wizard_created,
          color: 'success',
        });
        setData({
          input: inputState.input ?? '',
          contexts: inputState.contexts ?? [],
        });

        inputState.setChatId(data.id);
        isWizardFetching.current = false;
        setWizard(data as AIWizardVO);
        setInitError(null);
      }
    },
    [spaceId],
  );

  // React.useEffect(() => {
  //   if (isCopilot && !inputState.chatId) {
  //     newWizardCreatedRef.current = false;
  //   }
  // }, [isCopilot, inputState.chatId]);

  // 初始化 Dialog
  React.useEffect(() => {
    const fetchWizard = async () => {
      try {
        await initDialog(inputState.chatId);
      } catch (error) {
        isWizardFetching.current = false;
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('not found')) {
          // 清除缓存的 chatId
          inputState.clear();
          startNewChat?.(); // 切换到欢迎界面
          return;
        }
        setInitError(errorMessage);
      }
    };
    // fetch 时会销毁 AIChatUIRenderer 导致 doAppendMessage 失效
    if ((!inputState.chatId || wizard?.id !== inputState.chatId) && !isWizardFetching.current) {
      fetchWizard();
    }
  }, [inputState.chatId, wizard?.id, isWizardFetching.current]);

  // 初始化显示的消息
  const initialMessages = React.useMemo(() => {
    const msgs: AIMessageBO[] = [];
    if (wizard) {
      let i = 0;
      for (const msg of wizard.messages) {
        msgs.push(msg);
        i++;
      }
    }
    return msgs;
  }, [wizard, lang, props.forceLocale]);

  if (initError) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 mb-4">{initError}</div>
        <Button
          onClick={() => {
            initDialog();
          }}
          startDecorator={<AddOutlined color="var(--text-secondary)" />}
        >
          {t.ai.new_chat}
        </Button>
      </div>
    );
  }

  if (newWizardCreatedRef.current && !inputState.chatId) return <Skeleton pos="CHAT" />;
  if (isWizardFetching.current || !wizard) return <Skeleton pos="CHAT" />;

  return (
    <>
      <AIChatUIRenderer
        inputState={inputState}
        ref={chatUIHandle}
        // options={wizard.options}
        displayMode={props.displayMode}
        config={{ ...props.config, options: [...(props.config?.options || []), ...(wizard.options || [])] }}
        disabled={wizard.resolutionStatus === 'SUCCESS'}
        api={'/api/ai/chat'}
        initialMessages={initialMessages}
        skillsetIcons={props.skillsetIcons}
        onFinish={(_message) => {
          // trpc.ai.fetchWizard.query({ wizardId: wizard.id }).then((data) => setWizard(data as AIWizardVO));
        }}
        // textFilter={(text) => {
        //   if (text.startsWith('/resolve:')) {
        //     return `🖱️🖱️🖱️`; //  ${text}`;
        //   }
        //   return text;
        // }}
        // chatId={wizard.id}
        customBody={
          wizard
            ? {
                forceLocale: props.forceLocale,
                // initUserMessage: props.initUserMessage,
                // use previous dialog, or new dialog?
                // if new dialog, what's the intent type?
                initAIIntent: props.initAIIntent,
              }
            : undefined
        }
      />
    </>
  );
}

export const AIChatView = React.memo(React.forwardRef(InternalAIChatView));
